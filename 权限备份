import com.shiliangyun.framework.common.workflow.WorkflowLogic;

list=getListJSONObjects(obj,"list");


/**权限必要的字段，需要前端配置出来
paths=new ArrayList();
paths.add("refdocumentCategory_DMS.parentPathIds");
paths.add("creatorId");
paths.add("drafterID_DMS");
paths.add("masterWorkflow.approverUserIds");
paths.add("masterWorkflow.requiredUserIds");
paths.add("masterWorkflow.rejectorUserIds");
paths.add("flowFlag");
paths.add("collaborativeEditingIDs_DMS");
paths.add("SharingDepartmentIDs_DMS");
paths.add("masterWorkflowId");
paths.add("refdocumentCategory_DMS.parentPathIds");
FormLogic.getJsonFieldsValues(list,paths,"DocumentManagement_DMS",user);
**/

masterWorkflowIds=ExpressUtils.getListLongFromJSONObjs(list,"masterWorkflowId");
result=ExpressUtils.getObjectsByIds("Workflow",masterWorkflowIds,user);
if(!result.isSuccessful()){
    return result;
}

workflows=result.getContents();

documentCategoryIDs=ExpressUtils.getListLongFromJSONObjs(list,"documentCategoryID_DMS");
result=ExpressUtils.getObjectsByIds("DocumentCategory_DMS",documentCategoryIDs,user);
if(!result.isSuccessful()){
    return result;
}

documentCategories=result.getContents();

/**可查看**/
checkCanViewIds=new ArrayList();/**需要额外查询的ids**/
for(i=0;i<list.size();i++){
    item=list.get(i);
    id=item.getLong("id");

    workflowId=item.getLong("masterWorkflowId");
    if(!NumberParser.isNullOrZero(workflowId)){
        w=ExpressUtils.getFirstByLong(workflows,"","id",workflowId,user);
        if(w!=null){
            item.put("masterWorkflow",w);
        }
    }
    
    documentCategoryID=item.getLong("documentCategoryID_DMS");
    if(!NumberParser.isNullOrZero(documentCategoryID)){
        category=ExpressUtils.getFirstByLong(documentCategories,"","id",documentCategoryID,user);
        if(category!=null){
            item.put("refdocumentCategory_DMS",category);
        }
    }
    
    canView=false;
    canEdit=false;
    canPrint=false;
    isControl=false;
    if(item.getJSONObject("refdocumentCategory_DMS")!=null){
        parentIds=getListLongFromJSONObj(item.getJSONObject("refdocumentCategory_DMS"),"parentPathIds");
        /** if(!ListExtensions.isListNullOrEmpty(parentIds)&&parentIds.contains(575989288236220416L)){ **/
             if(item.getBooleanValue("YSFL_lyg_baosteeluat")){
            isControl=true;
        }
    }
    
    shareDepartmentIds=getListLongFromJSONObj(item,"SharingDepartmentIDs_DMS");
    collaborativeEditingIDs=getListLongFromJSONObj(item,"collaborativeEditingIDs_DMS");
    masterWorkflow=item.getJSONObject("masterWorkflow");
    approverUserIds=null;
    requiredUserIds=null;
    rejectorUserIds=null;
    if(masterWorkflow!=null){
        approverUserIds=getListLongFromJSONObj(masterWorkflow,"approverUserIds");
        requiredUserIds=getListLongFromJSONObj(masterWorkflow,"requiredUserIds");
        rejectorUserIds=getListLongFromJSONObj(masterWorkflow,"rejectorUserIds");
    }

    if(user.getIsAdmin()){
        canView=true;
    }
    else if((item.getLong("creatorId")!=null&&item.getLong("creatorId").equals(user.getId()))|| (item.getLong("drafterID_DMS")!=null&&item.getLong("drafterID_DMS").equals(user.getId()))){
        canView=true;
    }
    else if(!ListExtensions.isListNullOrEmpty(ExpressUtils.getIntersection(shareDepartmentIds,user.getDepartmentIds()))) {
         canView=true;
    }
    else if(!ListExtensions.isListNullOrEmpty(collaborativeEditingIDs)&&collaborativeEditingIDs.contains(user.getId())) {
         canView=true;
    }
    else if(!ListExtensions.isListNullOrEmpty(approverUserIds)&&approverUserIds.contains(user.getId())) {
         canView=true;
    }
    else if(!ListExtensions.isListNullOrEmpty(requiredUserIds)&&requiredUserIds.contains(user.getId())) {
         canView=true;
    }
    else if(!ListExtensions.isListNullOrEmpty(rejectorUserIds)&&rejectorUserIds.contains(user.getId())) {
         canView=true;
    }
    else{
        if(item.getLong("flowFlag").equals(0L)&&!ListExtensions.isListNullOrEmpty(requiredUserIds)){
            /**如果是流程审批中的还要检测是否是代理审批**/
            repIds=WorkflowLogic.getRepresentiveUserIds(user);
            if(!ListExtensions.isListNullOrEmpty(repIds)&&!ListExtensions.isListNullOrEmpty(ExpressUtils.getIntersection(repIds,requiredUserIds))){
                canView=true;
            }
        }

        if(!canView){
            checkCanViewIds.add(id);
        }
    }
    if(isControl){
        canEdit=canView;
        canPrint=canView;
    }
    else if((item.getLong("creatorId")!=null&&item.getLong("creatorId").equals(user.getId()))||(!ListExtensions.isListNullOrEmpty(collaborativeEditingID)&&collaborativeEditingID.contains(user.getId()))){
        canEdit=true;
    }

    if(user.getSharedDataLink()!=null){
        /**匿名分享**/
        canView=true;
        canEdit=false;
        canPrint=false;
    }
    
    item.put("canView",canView);
    item.put("canEdit",canEdit);
    item.put("canPrint",canPrint);
    item.put("canControl",isControl);
}

if(!ListExtensions.isListNullOrEmpty(checkCanViewIds)){
    sql=getReadSqlExpression("DocumentInteraction_DMS",user);
    sql.comparsionAnd("documentManagementID_DMS",ComparsionOperationEnum.EXIST,checkCanViewIds);
    sql.comparsionAnd("distributionTime_DMS",ComparsionOperationEnum.LESS_OR_EQUAL,DateTimeExtensions.getUTCNowString());
    sql.comparsionAnd("receivingStatus_DMS",ComparsionOperationEnum.EQUAL,1);
    /**类型是借阅**/
    sql.comparsionAnd("freeNumber1_DMS",ComparsionOperationEnum.EQUAL,1);
    sql.comparsionAnd("receivingTime_DMS",ComparsionOperationEnum.GREAT_OR_EQUAL,DateTimeExtensions.getUTCNowString());
    sql.comparsionAnd("creatorId",ComparsionOperationEnum.EQUAL,user.getId());
    sql.comparsionAnd("pagecode",ComparsionOperationEnum.EQUAL,2);
    result=getAllObjects("DocumentInteraction_DMS",sql,user);
   
    if(!result.isSuccessful()){
        return result;
    }
    else if(result.getContent()!=null){
        temp=new ArrayList();
        for(i=0;i<checkCanViewIds.size();i++){
            id=checkCanViewIds.get(i);
            existed=ExpressUtils.getFirstByLong(result.getContents(),"","documentManagementID_DMS",id,user);
            if(existed!=null){
                item=ExpressUtils.getFirstByLong(list,"","id",id,user);
                item.put("canView",true);
            }
            else{
                temp.add(id);
            }
        }

        checkCanViewIds=temp;
    }
}

if(!ListExtensions.isListNullOrEmpty(checkCanViewIds)){
    sql=getReadSqlExpression("DocumentInteraction_DMS",user);
    sql.comparsionAnd("documentManagementID_DMS",ComparsionOperationEnum.EXIST,checkCanViewIds);    
    sql.comparsionAnd("receivingStatus_DMS",ComparsionOperationEnum.EQUAL,1);
    sql.comparsionAnd("pagecode",ComparsionOperationEnum.EQUAL,1);
    /**发放部门receiveDepId_DMS包含用户部门，和用户是or的关系**/
    c1 =SqlExpression.generateComparsionConditionByValue("receivierID_DMS",ComparsionOperationEnum.EQUAL,user.getId(),FieldTypeEnum.BIGINT, FieldTypeEnum.BIGINT);
    c2 =SqlExpression.generateComparsionConditionByValue("receiveDepId_DMS",ComparsionOperationEnum.EXIST,user.getDepartmentIds(),FieldTypeEnum.BIGINT, FieldTypeEnum.BIGINT_ARRAY);
    sql.comparsion(false, true, SqlExpression.generateComparsionCondition(false,false,null, Arrays.asList(c1,c2)));
    result=getAllObjects("DocumentInteraction_DMS",sql,user);
    if(!result.isSuccessful()){
        return result;
    }
    else if(result.getContent()!=null){
        for(i=0;i<checkCanViewIds.size();i++){
            id=checkCanViewIds.get(i);
            existed=ExpressUtils.getFirstByLong(result.getContents(),"","documentManagementID_DMS",id,user);
            if(existed!=null){
                item=ExpressUtils.getFirstByLong(list,"","id",id,user);
                item.put("canView",true);
                item.put("canPrint",true);
            }
        }
    }
}

return true;